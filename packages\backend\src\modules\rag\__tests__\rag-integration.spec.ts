/**
 * RAG System Integration Tests
 * 
 * Comprehensive integration tests for the complete RAG workflow
 * from backend to rag-system package integration.
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { RagService } from '../rag.service';
import { RagController } from '../rag.controller';
import { getTestConfig, shouldSkipExternalServices } from '../../../test/test-config.util';

describe('RAG System Integration Tests', () => {
  let ragService: RagService;
  let ragController: RagController;
  let configService: ConfigService;
  let testConfig: ReturnType<typeof getTestConfig>;

  beforeAll(() => {
    if (shouldSkipExternalServices()) {
      console.log('Skipping RAG integration tests - external services disabled');
      return;
    }
    
    try {
      testConfig = getTestConfig();
    } catch (error) {
      console.log('Skipping RAG integration tests - configuration missing:', error);
      return;
    }
  });

  beforeEach(async () => {
    if (shouldSkipExternalServices() || !testConfig) {
      return;
    }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RagService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                'EMBEDDING_PROVIDER': testConfig.embedding.apiKey ? 'vllm' : 'mock',
                'EMBEDDING_API_KEY': testConfig.embedding.apiKey,
                'EMBEDDING_BASE_URL': testConfig.embedding.baseUrl,
                'EMBEDDING_MODEL': testConfig.embedding.model,
                'EMBEDDING_DIMENSIONS': testConfig.embedding.dimensions,
                'EMBEDDING_BATCH_SIZE': testConfig.embedding.batchSize,
                'PINECONE_API_KEY': testConfig.pinecone.apiKey,
                'PINECONE_INDEX_NAME': testConfig.pinecone.indexName,
                'PINECONE_DIMENSION': testConfig.pinecone.dimension,
                'RAG_ENABLED': 'true',
                'RAG_MAX_RESULTS': '5',
                'RAG_SIMILARITY_THRESHOLD': '0.7',
                'RAG_CONTEXT_WINDOW': '4000',
                'RAG_AUTO_INIT': 'false', // Don't auto-initialize in tests
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
      ],
      controllers: [RagController],
    }).compile();

    ragService = module.get<RagService>(RagService);
    ragController = module.get<RagController>(RagController);
    configService = module.get<ConfigService>(ConfigService);
  });

  describe('RAG Service Initialization', () => {
    it('should initialize RAG service successfully', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      await expect(ragService.initializeWithFaqData([])).resolves.not.toThrow();
    }, 60000);

    it('should handle initialization with custom FAQ data', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const customFaqData = [
        {
          id: 'test-1',
          question: 'How do I reset my password?',
          answer: 'You can reset your password by clicking the "Forgot Password" link on the login page.',
          category: 'Authentication',
          metadata: { priority: 'high' },
        },
        {
          id: 'test-2',
          question: 'How do I create a new ticket?',
          answer: 'To create a new ticket, go to the "New Ticket" section and fill out the required fields.',
          category: 'Tickets',
          metadata: { priority: 'medium' },
        },
      ];

      await expect(ragService.initializeWithFaqData(customFaqData)).resolves.not.toThrow();
    }, 60000);
  });

  describe('FAQ Search Functionality', () => {
    beforeEach(async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        return;
      }
      
      // Initialize the service before each test
      await ragService.initializeWithFaqData([]);
    });

    it('should search for relevant FAQs', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const query = 'password reset';
      const results = await ragService.searchRelevantFaqs(query, 3);

      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeLessThanOrEqual(3);
      
      if (results.length > 0) {
        expect(results[0]).toHaveProperty('faqEntry');
        expect(results[0]).toHaveProperty('similarityScore');
        expect(results[0]).toHaveProperty('relevanceRank');
        expect(results[0].faqEntry).toHaveProperty('question');
        expect(results[0].faqEntry).toHaveProperty('answer');
        expect(typeof results[0].similarityScore).toBe('number');
        expect(results[0].similarityScore).toBeGreaterThanOrEqual(0);
        expect(results[0].similarityScore).toBeLessThanOrEqual(1);
      }
    }, 30000);

    it('should return empty results for irrelevant queries', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const query = 'completely irrelevant quantum physics question about black holes';
      const results = await ragService.searchRelevantFaqs(query, 5);

      expect(Array.isArray(results)).toBe(true);
      // Results might be empty or have very low similarity scores
      if (results.length > 0) {
        expect(results[0].similarityScore).toBeLessThan(0.5);
      }
    }, 30000);

    it('should perform hybrid search with filters', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const query = 'ticket';
      const filters = { category: 'Tickets' };
      const results = await ragService.hybridSearch(query, filters, 3);

      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeLessThanOrEqual(3);
      
      // Verify structure
      if (results.length > 0) {
        expect(results[0]).toHaveProperty('faqEntry');
        expect(results[0]).toHaveProperty('similarityScore');
        expect(results[0]).toHaveProperty('relevanceRank');
      }
    }, 30000);
  });

  describe('RAG Statistics and Health', () => {
    beforeEach(async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        return;
      }
      
      await ragService.initializeWithFaqData([]);
    });

    it('should return RAG system statistics', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const stats = await ragService.getStatistics();

      expect(stats).toHaveProperty('totalFaqEntries');
      expect(stats).toHaveProperty('vectorDimensions');
      expect(stats).toHaveProperty('lastUpdated');
      expect(stats).toHaveProperty('indexHealth');
      
      expect(typeof stats.totalFaqEntries).toBe('number');
      expect(stats.totalFaqEntries).toBeGreaterThanOrEqual(0);
      expect(stats.vectorDimensions).toBe(testConfig.embedding.dimensions);
      expect(['healthy', 'degraded', 'unhealthy']).toContain(stats.indexHealth);
    }, 30000);

    it('should check if RAG service is available', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const isAvailable = await ragService.isAvailable();
      expect(typeof isAvailable).toBe('boolean');
    }, 30000);
  });

  describe('RAG Controller Integration', () => {
    beforeEach(async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        return;
      }
    });

    it('should handle search requests through controller', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const searchDto = {
        query: 'password',
        topK: 3,
      };

      const response = await ragController.searchFaqs(searchDto);

      expect(response).toHaveProperty('results');
      expect(response).toHaveProperty('query');
      expect(response).toHaveProperty('totalResults');
      expect(response).toHaveProperty('processingTime');
      
      expect(Array.isArray(response.results)).toBe(true);
      expect(response.query).toBe(searchDto.query);
      expect(typeof response.totalResults).toBe('number');
      expect(typeof response.processingTime).toBe('number');
    }, 30000);

    it('should handle initialization requests through controller', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const initDto = { faqData: [] };
      const response = await ragController.initialize(initDto);

      expect(response).toHaveProperty('message');
      expect(response).toHaveProperty('initialized');
      expect(response).toHaveProperty('timestamp');
      
      expect(response.initialized).toBe(true);
      expect(typeof response.message).toBe('string');
      expect(typeof response.timestamp).toBe('string');
    }, 60000);

    it('should return statistics through controller', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      const response = await ragController.getStatistics();

      expect(response).toHaveProperty('statistics');
      expect(response).toHaveProperty('timestamp');
      expect(response).toHaveProperty('status');
      
      expect(response.statistics).toHaveProperty('totalFaqEntries');
      expect(response.statistics).toHaveProperty('vectorDimensions');
      expect(response.statistics).toHaveProperty('indexHealth');
    }, 30000);
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty queries gracefully', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      await ragService.initializeWithFaqData([]);
      
      const results = await ragService.searchRelevantFaqs('', 5);
      expect(Array.isArray(results)).toBe(true);
    }, 30000);

    it('should handle very long queries', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      await ragService.initializeWithFaqData([]);
      
      const longQuery = 'a'.repeat(10000); // Very long query
      const results = await ragService.searchRelevantFaqs(longQuery, 5);
      expect(Array.isArray(results)).toBe(true);
    }, 30000);

    it('should handle special characters in queries', async () => {
      if (shouldSkipExternalServices() || !testConfig) {
        console.log('Skipping test - external services disabled');
        return;
      }

      await ragService.initializeWithFaqData([]);
      
      const specialQuery = '!@#$%^&*()_+{}|:"<>?[]\\;\',./ password reset';
      const results = await ragService.searchRelevantFaqs(specialQuery, 5);
      expect(Array.isArray(results)).toBe(true);
    }, 30000);
  });
});
